<section class="gallery">
  <div id="gallery" style="visibility: hidden; height: 1px; overflow: hidden; margin-left: 35px; margin-right: 35px">
    {{ $images := slice }}
    {{ range $image := .Resources.ByType "image" }}
      {{ $title := "" }}
      {{ $date := "" }}
      {{ $info := "" }}
      {{ with $image.Exif }}
        {{ $date = .Date }}
        {{ with .Tags.ImageDescription }}
          {{/* Title from EXIF ImageDescription */}}
          {{ $title = . }}
          {{ $info = . }}
        {{ end }}
        {{ with .Tags.Make }}
          {{ $info = (print $info "</BR>Maker: " (upper .))}}
        {{ end }}
        {{ with .Tags.Model }}
          {{ $info = (print $info "</BR>Model: " .)}}
        {{ end }}
        {{ with .Tags.ExposureTime }}
          {{ $expTime := . }}
          {{ if strings.Contains $expTime "/" }}
            {{ $tmp := (strings.Split $expTime "/") }}
            {{ $num := cast.ToFloat (index $tmp 0) }}
            {{ $denom := cast.ToFloat (index $tmp 1) }}
            {{ if gt $denom 0 }}
              {{ $fraction := math.Div $denom $num }}
              {{ $roundedFraction := math.Round $fraction }}
              {{ $info = (print $info "</BR>Exposure Time: 1/" $roundedFraction "s") }}
            {{ else }}
              {{ $info = (print $info "</BR>Exposure Time: " $expTime "s") }}
            {{ end }}
          {{ else }}
            {{ $info = (print $info "</BR>Exposure Time: " $expTime "s") }}
          {{ end }}
        {{ end }}
        {{ with .Tags.FNumber }}
          {{ $tmp := (strings.Split . "/") }}
          {{ $val1 := cast.ToFloat (index $tmp 0) }}
          {{ $val2 := cast.ToFloat (index $tmp 1) }}
          {{ $val3 := $val1}}
          {{if $val2}}
            {{ $val3 = math.Div $val1 $val2 }}
          {{end}}
          {{ $info = (print $info "</BR>F-stop: f/" $val3)}}
        {{ end }}
        {{ with .Tags.ISO }}
          {{ $info = (print $info "</BR>ISO: " .)}}
        {{ end }}
        {{ with .Tags.FocalLength }}
          {{ $tmp := (strings.Split . "/") }}
          {{ $val1 := cast.ToFloat (index $tmp 0) }}
          {{ $val2 := cast.ToFloat (index $tmp 1) }}
          {{ $val3 := $val1}}
          {{if $val2}}
            {{ $val3 = math.Ceil (math.Div $val1 $val2) }}
          {{end}}
          {{ $info = (print $info "</BR>Focal Length: " $val3 "mm")}}
        {{ end }}
        {{ with .Tags.FocalLengthIn35mmFormat }}
          {{ $info = (print $info "</BR>35mm Focal Length: " . "mm")}}
        {{ end }}
      {{ end }}
      {{ if ne $image.Title $image.Name }}
        {{/* Title from front matter */}}
        {{ $title = $image.Title }}
      {{ end }}
      {{ if $image.Params.Date }}
        {{/* Date from front matter */}}
        {{ $date = time $image.Params.Date }}
      {{ end }}
      {{ $images = $images | append (dict
        "Name" $image.Name
        "Title" $title
        "Info" $info
        "Date" $date
        "image" $image
        "Params" $image.Params
        )
      }}
    {{ end }}
    {{ range sort $images (.Params.sort_by | default "Name") (.Params.sort_order | default "asc") }}
      {{ $image := .image }}
      {{ $thumbnail := $image.Filter (slice images.AutoOrient (images.Process "fit 600x600")) }}

      {{/* BARBADO: Can use original image, since they were scaled with external tool (https://www.xnview.com/en/xnconvert/) to fit 1920x1920 pixels */}}
      {{/* $full := $image.Filter (slice images.AutoOrient (images.Process "fit 1600x1600")) */}}

      {{ $full := $image }}

      {{ $color := index $thumbnail.Colors 0 | default "transparent" }}
      <a class="gallery-item" href="{{ $image.RelPermalink }}" data-pswp-src="{{ $full.RelPermalink }}" data-pswp-width="{{ $full.Width }}" data-pswp-height="{{ $full.Height }}" data-pswp-target="{{ $image.Name | urlize }}" title="{{ .Title }}" itemscope itemtype="https://schema.org/ImageObject" style="aspect-ratio: {{ $thumbnail.Width }} / {{ $thumbnail.Height }}">
        <figure style="background-color: {{ $color }}; aspect-ratio: {{ $thumbnail.Width }} / {{ $thumbnail.Height }}">
          <img class="lazyload" width="{{ $thumbnail.Width }}" height="{{ $thumbnail.Height }}" data-src="{{ $thumbnail.RelPermalink }}" alt="{{ .Info }}" />
        </figure>
        <meta itemprop="contentUrl" content="{{ $image.RelPermalink }}" />
        {{ with site.Params.Author }}
          <span itemprop="creator" itemtype="https://schema.org/Person" itemscope>
            <meta itemprop="name" content="{{ site.Params.Author.name }}" />
          </span>
        {{ end }}
      </a>
    {{ end }}
  </div>
</section>

