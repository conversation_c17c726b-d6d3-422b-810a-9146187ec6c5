{{ if .Site.IsMultiLingual }}
<div class="language-switcher">
  {{ range .Site.Languages }}
    {{ if ne .Lang $.Site.Language.Lang }}
      {{ with $.Site.GetPage "/" }}
        {{ with .Translations.ByLanguage .Lang }}
          <a href="{{ .Permalink }}" class="language-link" title="{{ .Language.LanguageName }}" hreflang="{{ .Language.Lang }}">
            <span class="flag flag-{{ .Language.Lang }}"></span>
          </a>
        {{ else }}
          <a href="{{ absLangURL "/" .Lang }}" class="language-link" title="{{ .LanguageName }}" hreflang="{{ .Lang }}">
            <span class="flag flag-{{ .Lang }}"></span>
          </a>
        {{ end }}
      {{ end }}
    {{ else }}
      <span class="language-link current" title="{{ .LanguageName }}">
        <span class="flag flag-{{ .Lang }}"></span>
      </span>
    {{ end }}
  {{ end }}
</div>
{{ end }}
