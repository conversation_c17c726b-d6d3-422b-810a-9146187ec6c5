/* Floating language switcher in top-left corner */
.language-switcher-floating {
  position: fixed;
  top: 1rem;
  right: 3rem;
  z-index: 1000;
  display: flex;
  gap: 0.5rem;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Dark mode support for floating switcher */
@media (prefers-color-scheme: dark) {
  .language-switcher-floating {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

.language-link {
  display: inline-block;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  text-decoration: none;
  opacity: 0.7;
}

.language-link:hover {
  opacity: 1;
  transform: scale(1.1);
}

.language-link.current {
  opacity: 1;
  cursor: default;
  background: rgba(0, 0, 0, 0.1);
}

/* Flag emojis */
.flag-emoji {
  display: inline-block;
  font-size: 1.5rem;
  line-height: 1;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .language-switcher-floating {
    top: 0.5rem;
    left: 0.5rem;
    gap: 0.25rem;
    padding: 0.25rem;
  }

  .flag-emoji {
    font-size: 1.25rem;
  }
}

/* Ensure the switcher doesn't interfere with content */
body {
  padding-top: 0;
}

/* High contrast colours
:root {
  --surface-1-light: #fff;
  --surface-2-light: #f5f5f5;
  --text-1-light: #000;
  --text-2-light: #737373;
  --border-light: #bbb;
  --surface-1-dark: #000;
  --surface-2-dark: #171717;
  --text-1-dark: #fff;
  --text-2-dark: #a3a3a3;
  --border-dark: #606060;
}
*/

/* Set maximum width for galleries to 1536px, add padding
section.gallery {
  max-width: 1536px;
  @media (min-width: 640px) {
    padding-right: 1.5rem;
    padding-left: 1.5rem;
  }
}
*/

/* Larger header with circled buttons
body > header {
  padding: 1.5rem;
  .btn-square {
    background: var(--surface-2);
    border-radius: 9999px;
  }
  margin-bottom: 0;
}
*/
