/* High contrast colours
:root {
  --surface-1-light: #fff;
  --surface-2-light: #f5f5f5;
  --text-1-light: #000;
  --text-2-light: #737373;
  --border-light: #bbb;
  --surface-1-dark: #000;
  --surface-2-dark: #171717;
  --text-1-dark: #fff;
  --text-2-dark: #a3a3a3;
  --border-dark: #606060;
}
*/

/* Set maximum width for galleries to 1536px, add padding
section.gallery {
  max-width: 1536px;
  @media (min-width: 640px) {
    padding-right: 1.5rem;
    padding-left: 1.5rem;
  }
}
*/

/* Larger header with circled buttons
body > header {
  padding: 1.5rem;
  .btn-square {
    background: var(--surface-2);
    border-radius: 9999px;
  }
  margin-bottom: 0;
}
*/
