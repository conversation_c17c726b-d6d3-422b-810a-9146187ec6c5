html {
  height: 100%;
}

body {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--surface-1);
  color: var(--text-1);
}

body > header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  margin-bottom: 2rem;
  width: 100%;
  min-height: 4rem;

  ul {
    display: inline-flex;
  }

  .btn {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    border-radius: 0.5rem;
    padding-right: 1rem;
    padding-left: 1rem;
    height: 3rem;
    font-weight: 600;
    font-size: 1.25rem;
    user-select: none;
  }

  .btn-square {
    padding: 0;
    width: 3rem;
  }
}

body > menu {
  margin: 0 auto 5rem;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
  width: 100%;
  max-width: 768px;
  color: var(--text-2);
  font-weight: 600;
  font-size: 1.25rem;
  line-height: 1.75rem;
  user-select: none;
  text-align: center;

  a {
    display: block;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;

    &:hover,
    &[aria-current="true"] {
      color: var(--text-1);
    }
  }
}

body > main {
  flex-grow: 1;
  width: 100%;
  height: 100%;
}

main > section {
  margin: 0 auto 0;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
}

body > footer {
  margin-right: auto;
  margin-left: auto;
  color: var(--text-2);
  font-size: 0.875rem;
  line-height: 1.25rem;

  section:last-of-type {
    display: flex;
    flex-flow: row wrap;
    justify-content: center;
    gap: 1rem;
    padding: 2.5rem;

    a:hover {
      text-decoration-line: underline;
    }
  }
}

.float{
  position:fixed;
  width:24px;
  height:24px;
  top:20px;
  left:20px;
  color:#000;
  text-align:center;
}

hgroup {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  margin: 3rem auto 3rem;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
  max-width: 1024px;
  text-align: center;

  a {
    align-self: center;
    padding-right: 1.5rem;
  }

  h1 {
    display: inline-flex;
    align-self: center;
    font-weight: 700;
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  h2 {
    font-weight: 700;
    font-size: 1.5rem;
    line-height: 2rem;
  }

  p {
    color: var(--text-2);
    text-wrap: balance;
  }

  @media (min-width: 640px) {
    width: 83.3333%;
  }

  @media (min-width: 768px) {
    h1 {
      font-size: 2.25rem;
      line-height: 2.5rem;
    }

    h2 {
      font-size: 1.875rem;
      line-height: 2.25rem;
    }

    p {
      font-size: 1.125rem;
      line-height: 1.75rem;
    }
  }
}

section.galleries {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 2rem 1.5rem;
  max-width: 1280px;

  @media (min-width: 640px) {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    row-gap: 3rem;
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

section.gallery {
  padding-right: 0;
  padding-left: 0;
  margin-left: 15px;
  margin-right: 15px;
}

.prose {
  max-width: 768px;
  color: var(--text-1);
  font-size: 1rem;
  line-height: 1.75;

  a {
    color: var(--text-1);
    font-weight: 500;
    text-decoration: underline;
  }

  p {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
  }

  img {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  h3 {
    margin-top: 1.6em;
    margin-bottom: 0.6em;
    color: var(--text-1);
    font-weight: 600;
    font-size: 1.25em;
    line-height: 1.6;
  }

  ul {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
    padding-left: 1.625em;
    list-style-type: disc;

    & > li {
      margin-top: 0.5em;
      margin-bottom: 0.5em;
      padding-left: 0.375em;
    }

    li::marker {
      color: var(--text-2);
      font-variant-numeric: tabular-nums;
      unicode-bidi: isolate;
      text-align: start !important;
      text-align-last: start !important;
      text-indent: 0 !important;
      text-transform: none;
    }
  }

  h3 + * {
    margin-top: 0;
  }

  figure {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  figure > * {
    margin-top: 0;
    margin-bottom: 0;
  }

  > :first-child {
    margin-top: 0;
  }

  > :last-child {
    margin-bottom: 0;
  }
}

.card {
  display: flex;
  flex-direction: column;
  border-radius: 1rem;

  & > figure {
    aspect-ratio: 3/2;
    width: 100%;
    border-radius: 1rem;
  }

  & > img, & figure > img {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    border-radius: 1rem;
    aspect-ratio: 3/2;
    width: 100%;
    overflow: hidden;
    object-fit: cover;

    &:hover {
      box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -4px rgba(0, 0, 0, 0.1);
    }
  }

  & > div {
    display: flex;
    flex: 1 1 auto;
    flex-direction: row;
    gap: 0.75rem;
    padding: 1rem;

    & > h2 {
      font-weight: 600;
      font-size: 1.125rem;
      line-height: 1.375;
    }

    & > p {
      color: var(--text-2);
      font-size: 0.875rem;
      line-height: 1.8rem;
      align-self: center;
    }
  }
}

.gallery-item {
  cursor: zoom-in;
}

.hidden {
  display: none;
}

.group[aria-expanded="true"] {
  .group-aria-expanded\:block {
    display: block;
  }

  .group-aria-expanded\:hidden {
    display: none;
  }
}

section.social-icons {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  justify-content: center;
  margin-top: 2rem;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
}

section.featured {
  padding-right: 1.5rem;
  padding-left: 1.5rem;
  max-width: 1280px;
  color: var(--text-1-dark);
}

.featured-card {
  display: flex;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border-radius: 1rem;
  background-position: center;
  background-size: cover;
  aspect-ratio: 1 / 1;
  width: 100%;
  overflow: hidden;

  @media (min-width: 640px) {
    aspect-ratio: 16/9;
  }

  & > div {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    gap: 1rem;
    background-image: linear-gradient(to top, RGB(0 0 0 / 0.8) 10%, transparent 50%);
    padding: 1.5rem;
    width: 100%;
    height: 100%;

    & > h2 {
      font-weight: 700;
      font-size: 1.5rem;
      line-height: 1.25;

      @media (min-width: 768px) {
        font-size: 1.875rem;
        line-height: 2.25rem;
      }
    }

    & > p {
      font-size: 0.875rem;
      line-height: 1.25rem;
    }
  }

  &:hover {
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -4px rgba(0, 0, 0, 0.1);
  }
}

nav.categories {
  padding-right: 1.5rem;
  padding-left: 1.5rem;
  margin: 0 auto 3rem;

  & > ul {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 0.75rem;
    flex-wrap: wrap;

    li {
      max-width: 100%;

      & > a {
        display: block;
        padding: 0.5rem 0.75rem;
        border-radius: 9999px;
        border: 1px solid var(--border);
        font-size: 0.875rem;
        line-height: 1.25rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        @media (min-width: 640px) {
          padding: 0.75rem 1rem;
          font-size: 1rem;
        }
      }
    }
  }
}
