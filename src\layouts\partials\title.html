{{ partial "language-switcher.html" . }}

{{ with .Parent }}
<a class="btn btn-square float" href="{{ .RelPermalink | default .Site.Home.RelPermalink }}" title="{{ .Title }}">
  <svg width="32" height="32" data-slot="icon" fill="currentColor" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
    <path clip-rule="evenodd" fill-rule="evenodd" d="M11.03 3.97a.75.75 0 0 1 0 1.06l-6.22 6.22H21a.75.75 0 0 1 0 1.5H4.81l6.22 6.22a.75.75 0 1 1-1.06 1.06l-7.5-7.5a.75.75 0 0 1 0-1.06l7.5-7.5a.75.75 0 0 1 1.06 0Z"></path>
  </svg>
</a>
{{ end }}

{{ if .Title }}
  <hgroup>
    <h1>      {{ .Title }}    </h1>
    {{ with .Params.Description }}
      <p>{{ . | markdownify }}</p>
    {{ end }}
  </hgroup>
{{ end }}