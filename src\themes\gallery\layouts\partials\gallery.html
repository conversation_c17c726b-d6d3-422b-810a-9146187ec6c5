<section class="gallery">
  <div id="gallery" style="visibility: hidden; height: 1px; overflow: hidden">
    {{ $images := slice }}
    {{ range $image := .Resources.ByType "image" }}
      {{ $title := "" }}
      {{ $date := "" }}
      {{ with $image.Exif }}
        {{ $date = .Date }}
        {{ with .Tags.ImageDescription }}
          {{/* Title from EXIF ImageDescription */}}
          {{ $title = . }}
        {{ end }}
      {{ end }}
      {{ if ne $image.Title $image.Name }}
        {{/* Title from front matter */}}
        {{ $title = $image.Title }}
      {{ end }}
      {{ if $image.Params.Date }}
        {{/* Date from front matter */}}
        {{ $date = time $image.Params.Date }}
      {{ end }}
      {{ $images = $images | append (dict
        "Name" $image.Name
        "Title" $title
        "Date" $date
        "image" $image
        "Params" $image.Params
        )
      }}
    {{ end }}
    {{ range sort $images (.Params.sort_by | default "Name") (.Params.sort_order | default "asc") }}
      {{ $image := .image }}
      {{ $thumbnail := $image.Filter (slice images.AutoOrient (images.Process "fit 600x600")) }}
      {{ $full := $image.Filter (slice images.AutoOrient (images.Process "fit 1600x1600")) }}
      {{ $color := index $thumbnail.Colors 0 | default "transparent" }}
      <a class="gallery-item" href="{{ $image.RelPermalink }}" data-pswp-src="{{ $full.RelPermalink }}" data-pswp-width="{{ $full.Width }}" data-pswp-height="{{ $full.Height }}" data-pswp-target="{{ $image.Name | urlize }}" title="{{ .Title }}" itemscope itemtype="https://schema.org/ImageObject" style="aspect-ratio: {{ $thumbnail.Width }} / {{ $thumbnail.Height }}">
        <figure style="background-color: {{ $color }}; aspect-ratio: {{ $thumbnail.Width }} / {{ $thumbnail.Height }}">
          <img class="lazyload" width="{{ $thumbnail.Width }}" height="{{ $thumbnail.Height }}" data-src="{{ $thumbnail.RelPermalink }}" alt="{{ .Title }}" />
        </figure>
        <meta itemprop="contentUrl" content="{{ $image.RelPermalink }}" />
        {{ with site.Params.Author }}
          <span itemprop="creator" itemtype="https://schema.org/Person" itemscope>
            <meta itemprop="name" content="{{ site.Params.Author.name }}" />
          </span>
        {{ end }}
      </a>
    {{ end }}
  </div>
</section>
