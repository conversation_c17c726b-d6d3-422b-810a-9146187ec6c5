copyright = "© Cesar Barbado"
defaultContentLanguage = "en"
disableKinds = ["taxonomy"]
enableRobotsTXT = true
languageCode = "en"
# timeZone = "Europe/Berlin"
timeout = "120s"
title = "Gallery"

[params]
  # defaultTheme = "dark"
  defaultTheme = "light"
  description = "Barbado's Photo Gallery"
  title = "Barbado's Photo Gallery"
  [params.author]
    email = "<EMAIL>"
    name = "Cesar Barbado"
  [params.socialIcons]
    # facebook = "https://www.facebook.com/"
    # instagram = "https://www.instagram.com/"
    # github = "https://github.com/nicokaiser/hugo-theme-gallery/"
    # youtube = "https://www.youtube.com/"
    # email = "mailto:<EMAIL>"
    # website = "https://example.com"
    # mastodon = "https://example.com"
    # pixelfed = "https://example.com"
    # mixcloud = "https://mixcloud.com"
  [params.gallery]
    #boxSpacing = 10
    #targetRowHeight = 288
    #targetRowHeightTolerance = 0.25

[outputs]
  home = ["HTML", "RSS"]
  page = ["HTML"]
  section = ["HTML"]

[imaging]
  quality = 75
  resampleFilter = "CatmullRom"
  [imaging.exif]
    disableDate = false
    disableLatLong = true
    # includeFields = "ImageDescription|Orientation|Make|Model"
    includeFields = ""

[module]
  [module.hugoVersion]
    min = "0.121.2"
  [[module.imports]]
    # path = "github.com/nicokaiser/hugo-theme-gallery/v4"
    path = "../"

[menu]
  [[menu.footer]]
    # name = "GitHub"
    # url = "https://github.com/nicokaiser/hugo-theme-gallery/"
    # weight = 3

[services]
  [services.rss]
    limit = 100
