# DEVELOPMENT DOCKERFILE
# docker build -t hugo .
# docker run -it -p 1313:1313 -v ${pwd}/src:/app hugo

# hugo server -F --bind 0.0.0.0
# hugo -F

# Base image
FROM alpine

# Set the working directory
WORKDIR /app

RUN apk update
RUN apk add --no-cache bash
RUN apk add hugo
# RUN apk add --update --no-cache go git

# RUN apk add --update --no-cache go vim git make musl-dev curl
# RUN apk update
# RUN apk add wget

# Run command shell
CMD ["/bin/bash"]