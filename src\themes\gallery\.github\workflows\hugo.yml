name: Deploy to Pages

on:
  push:
    branches: ["main"]
  workflow_dispatch:

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      HUGO_VERSION: 0.124.0
    steps:
      - name: Install Hugo
        run: |
          wget -O ${{ runner.temp }}/hugo.deb https://github.com/gohugoio/hugo/releases/download/v${HUGO_VERSION}/hugo_extended_${HUGO_VERSION}_linux-amd64.deb \
          && sudo dpkg -i ${{ runner.temp }}/hugo.deb
      - name: Checkout
        uses: actions/checkout@v4
      - name: Cache example images
        id: cache-images
        uses: actions/cache@v4
        with:
          path: ./exampleSite/content
          key: images-${{ hashFiles('exampleSite/content/**') }}
      - name: Download example images
        if: steps.cache-images.outputs.cache-hit != 'true'
        working-directory: ./exampleSite
        run: ./pull-images.sh
      - name: Setup Pages
        id: pages
        uses: actions/configure-pages@v4
      - name: Build with <PERSON>
        working-directory: ./exampleSite
        env:
          HUGO_ENVIRONMENT: production
          HUGO_ENV: production
        run: hugo --gc --minify --baseURL "${{ steps.pages.outputs.base_url }}/"
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./exampleSite/public

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
