/* custom.css */

/* Header layout for language switcher */
header .header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

header .header-left {
  display: flex;
  align-items: center;
}

header .header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Language switcher styles */
.language-switcher {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.language-link {
  display: inline-block;
  padding: 0.25rem;
  border-radius: 4px;
  transition: opacity 0.2s ease;
  text-decoration: none;
}

.language-link:hover {
  opacity: 0.7;
}

.language-link.current {
  opacity: 1;
  cursor: default;
}

/* Flag icons using emoji */
.flag {
  display: inline-block;
  font-size: 1.5rem;
  line-height: 1;
}

.flag-pt::before {
  content: "🇧🇷";
}

.flag-en::before {
  content: "🇺🇸";
}

.flag-de::before {
  content: "🇩🇪";
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .language-switcher {
    gap: 0.25rem;
  }

  .flag {
    font-size: 1.25rem;
  }
}
