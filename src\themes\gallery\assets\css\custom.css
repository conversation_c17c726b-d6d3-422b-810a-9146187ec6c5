/* custom.css */

/* Floating language switcher in top-right corner */
.language-switcher-floating {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
  display: flex;
  gap: 0.5rem;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Dark mode support for floating switcher */
@media (prefers-color-scheme: dark) {
  .language-switcher-floating {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

.language-link {
  display: inline-block;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  text-decoration: none;
  opacity: 0.7;
}

.language-link:hover {
  opacity: 1;
  transform: scale(1.1);
}

.language-link.current {
  opacity: 1;
  cursor: default;
  background: rgba(0, 0, 0, 0.1);
}

/* Flag icons using emoji */
.flag {
  display: inline-block;
  font-size: 1.5rem;
  line-height: 1;
}

.flag-pt::before {
  content: "🇧🇷";
}

.flag-en::before {
  content: "🇺🇸";
}

.flag-de::before {
  content: "🇩🇪";
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .language-switcher-floating {
    top: 0.5rem;
    right: 0.5rem;
    gap: 0.25rem;
    padding: 0.25rem;
  }

  .flag {
    font-size: 1.25rem;
  }
}

/* Ensure the switcher doesn't interfere with content */
body {
  padding-top: 0;
}
