<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>{{ with .Title }}{{ . }} -{{ end }} {{ .Site.Title }}</title>
  <link rel="canonical" href="{{ .Permalink }}" />
  <link rel="icon" type="image/svg+xml" href="{{ "images/favicon.svg" | relURL }}" />
  <link rel="icon" type="image/png" href="{{ "images/favicon.png" | relURL }}" />
  <link rel="apple-touch-icon" sizes="180x180" href="{{ "images/apple-touch-icon.png" | relURL }}" />
  <meta name="description" content="{{ with .Description }}{{ . | markdownify | plainify }}{{ else }}{{ with .Site.Params.description }}{{ . | markdownify | plainify }}{{ end }}{{ end }}" />
  {{ if .Params.keywords }}
    <meta name="keywords" content="{{ delimit .Params.keywords `, ` }}" />
  {{ end }}
  {{ if .Params.private }}
    <meta name="robots" content="noindex, nofollow" />
  {{ else }}
    <meta name="robots" content="index, follow" />
  {{ end }}
  {{ range .AlternativeOutputFormats -}}
    {{ printf `<link rel="%s" type="%s" href="%s" title="%s" />` .Rel .MediaType.Type .Permalink $.Site.Title | safeHTML }}
  {{ end -}}
  {{ partial "opengraph.html" . }}
  {{ $css := resources.Get "/css/main.scss" | toCSS | minify | fingerprint }}
  <link rel="stylesheet" href="{{ $css.RelPermalink }}" />
  {{ $params := dict
    "closeTitle" (i18n "closeTitle")
    "zoomTitle" (i18n "zoomTitle")
    "arrowPrevTitle" (i18n "arrowPrevTitle")
    "arrowNextTitle" (i18n "arrowNextTitle")
    "errorMsg" (i18n "errorMsg")
    "downloadTitle" (i18n "downloadTitle")
    "boxSpacing" (default 8 .Site.Params.gallery.boxSpacing)
    "targetRowHeight" (default 288 .Site.Params.gallery.targetRowHeight)
    "targetRowHeightTolerance" (default 0.25 .Site.Params.gallery.targetRowHeightTolerance)
  }}
  {{ $js := resources.Get "js/main.js" | js.Build (dict "minify" true "params" $params) | resources.Fingerprint }}
  <script src="{{ $js.RelPermalink }}" defer></script>
  {{- partial "head-custom.html" . -}}
</head>
