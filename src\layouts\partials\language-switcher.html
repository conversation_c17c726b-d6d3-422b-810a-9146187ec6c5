{{ if hugo.IsMultilingual }}
<div class="language-switcher-floating">
  {{ range .Site.Languages }}
    {{ if ne .Lang $.Site.Language.Lang }}
      {{ $langURL := printf "/%s/" .Lang }}
      <a href="{{ $langURL | absURL }}" class="language-link" title="{{ .LanguageName }}" hreflang="{{ .Lang }}">
        {{ .Lang }}
      </a>
    {{ else }}
      <span class="language-link current" title="{{ .LanguageName }}">
        {{ .Lang }}
      </span>
    {{ end }}
  {{ end }}
</div>
{{ end }}