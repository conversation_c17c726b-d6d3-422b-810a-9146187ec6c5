{{ if hugo.IsMultilingual }}
<div class="language-switcher-floating">
  {{ range .Site.Languages }}
    {{ if ne .Lang $.Site.Language.Lang }}
      {{ with $.GetPage "." }}
        {{ with .Translations.ByLanguage .Lang }}
          <a href="{{ .Permalink }}" class="language-link" title="{{ .LanguageName }}" hreflang="{{ .Lang }}">
            {{ .Lang }}
          </a>
        {{ else }}
          {{ $langURL := printf "/album/%s%s" .Lang (strings.TrimPrefix "/album" $.RelPermalink) }}
          <a href="{{ $langURL }}" class="language-link" title="{{ .LanguageName }}" hreflang="{{ .Lang }}">
            {{ .Lang }}
          </a>
        {{ end }}
      {{ end }}
    {{ else }}
      <span class="language-link current" title="{{ .LanguageName }}">
        {{ .Lang }}
      </span>
    {{ end }}
  {{ end }}
</div>
{{ end }}