<!doctype html>
{{- $theme := .Params.theme | default .Site.Params.defaultTheme }}
<html class="{{- if (eq $theme "light") -}}light{{- else if (eq $theme "dark") }}dark{{ end }}" lang="{{- site.LanguageCode | default "en" -}}">
  {{ partial "head.html" . }}
  <body>
    {{ block "header" . }}{{ partial "header.html" . }}{{ end }}
    <!-- Floating Language Switcher -->
    {{ if .Site.IsMultiLingual }}
    <div class="language-switcher-floating">
      {{ range .Site.Languages }}
        {{ if ne .Lang $.Site.Language.Lang }}
          <a href="{{ "/" | absLangURL .Lang }}" class="language-link" title="{{ .LanguageName }}" hreflang="{{ .Lang }}">
            {{ if eq .Lang "pt" }}🇧🇷{{ else if eq .Lang "en" }}🇺🇸{{ else if eq .<PERSON> "de" }}🇩🇪{{ end }}
          </a>
        {{ else }}
          <span class="language-link current" title="{{ .LanguageName }}">
            {{ if eq .<PERSON> "pt" }}🇧🇷{{ else if eq .<PERSON> "en" }}🇺🇸{{ else if eq .<PERSON> "de" }}🇩🇪{{ end }}
          </span>
        {{ end }}
      {{ end }}
    </div>
    {{ end }}
    <main>
      {{ block "main" . }}{{ end }}
    </main>
    {{ block "footer" . }}{{ partialCached "footer.html" . }}{{ end }}
  </body>
</html>
